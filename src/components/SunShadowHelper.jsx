import { useRef, useEffect } from 'react';
import { useThree } from '@react-three/fiber';
import * as THREE from 'three';

/**
 * Sun Shadow Helper Component
 * Hiển thị helper để debug shadow camera và sun light direction
 */
export function SunShadowHelper({ 
  enabled = false, // Chỉ enable khi debug
  showShadowCamera = true,
  showLightDirection = true 
}) {
  const { scene } = useThree();
  const helpersRef = useRef([]);

  useEffect(() => {
    if (!enabled || !scene) return;

    // Clear existing helpers
    helpersRef.current.forEach(helper => {
      scene.remove(helper);
    });
    helpersRef.current = [];

    // Find directional lights in scene
    scene.traverse((child) => {
      if (child.isDirectionalLight && child.castShadow) {
        
        // Shadow Camera Helper
        if (showShadowCamera && child.shadow?.camera) {
          const shadowHelper = new THREE.CameraHelper(child.shadow.camera);
          shadowHelper.name = `shadow-helper-${child.name || 'directional'}`;
          scene.add(shadowHelper);
          helpersRef.current.push(shadowHelper);
        }

        // Light Direction Helper
        if (showLightDirection) {
          const dirHelper = new THREE.DirectionalLightHelper(child, 5, 0xffff00);
          dirHelper.name = `light-helper-${child.name || 'directional'}`;
          scene.add(dirHelper);
          helpersRef.current.push(dirHelper);
        }
      }
    });

    // Cleanup function
    return () => {
      helpersRef.current.forEach(helper => {
        scene.remove(helper);
        if (helper.dispose) helper.dispose();
      });
      helpersRef.current = [];
    };
  }, [enabled, scene, showShadowCamera, showLightDirection]);

  return null; // Component này không render gì
}

/**
 * Shadow Quality Indicator
 * Hiển thị thông tin về shadow quality hiện tại
 */
export function ShadowQualityIndicator({ enabled = false }) {
  const { gl } = useThree();

  useEffect(() => {
    if (!enabled || !gl?.shadowMap) return;

    const info = {
      enabled: gl.shadowMap.enabled,
      type: gl.shadowMap.type,
      autoUpdate: gl.shadowMap.autoUpdate,
      needsUpdate: gl.shadowMap.needsUpdate
    };

    console.log('🌞 Shadow Quality Info:', info);
    
    // Log shadow type name
    const typeNames = {
      [THREE.BasicShadowMap]: 'BasicShadowMap',
      [THREE.PCFShadowMap]: 'PCFShadowMap', 
      [THREE.PCFSoftShadowMap]: 'PCFSoftShadowMap',
      [THREE.VSMShadowMap]: 'VSMShadowMap'
    };
    
    console.log('🌞 Shadow Type:', typeNames[gl.shadowMap.type] || 'Unknown');
  }, [enabled, gl]);

  return null;
}

/**
 * Sun Position Controller
 * Component để điều khiển vị trí mặt trời (chỉ dùng khi debug)
 */
export function SunPositionController({ 
  enabled = false,
  onPositionChange = null 
}) {
  const { scene } = useThree();

  useEffect(() => {
    if (!enabled) return;

    const handleKeyPress = (event) => {
      if (!scene) return;

      let sunLight = null;
      scene.traverse((child) => {
        if (child.isDirectionalLight && child.castShadow && !sunLight) {
          sunLight = child;
        }
      });

      if (!sunLight) return;

      const step = 2;
      const currentPos = sunLight.position;

      switch(event.key.toLowerCase()) {
        case 'arrowup':
          sunLight.position.set(currentPos.x, currentPos.y + step, currentPos.z);
          break;
        case 'arrowdown':
          sunLight.position.set(currentPos.x, currentPos.y - step, currentPos.z);
          break;
        case 'arrowleft':
          sunLight.position.set(currentPos.x - step, currentPos.y, currentPos.z);
          break;
        case 'arrowright':
          sunLight.position.set(currentPos.x + step, currentPos.y, currentPos.z);
          break;
        case 'pageup':
          sunLight.position.set(currentPos.x, currentPos.y, currentPos.z + step);
          break;
        case 'pagedown':
          sunLight.position.set(currentPos.x, currentPos.y, currentPos.z - step);
          break;
        default:
          return;
      }

      console.log('🌞 Sun Position:', sunLight.position);
      if (onPositionChange) {
        onPositionChange(sunLight.position.clone());
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    
    // Log controls
    console.log('🌞 Sun Controls: Arrow keys (X/Y), PageUp/PageDown (Z)');

    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [enabled, scene, onPositionChange]);

  return null;
}
